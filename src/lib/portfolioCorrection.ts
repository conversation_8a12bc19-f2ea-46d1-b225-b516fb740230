import { UserStats, UserPurchase } from './contracts';

/**
 * Portfolio Metrics Correction Utility
 * 
 * This module provides functions to correct inflated portfolio metrics
 * for users who made purchases before the treasury allocation fix.
 * 
 * Background:
 * - Before the fix, treasury allocation (5% extra) was included in user stats
 * - This caused inflated portfolio values (e.g., 70+ trillion tokens for 100 USDT)
 * - The fix was deployed at timestamp: 2025-07-10T23:42:09.537Z
 * - Contract address with fix: 0xb1995f8C4Cf5409814d191e444e6433f5B6c712b
 * - Exchange rate calculation bug was also fixed in the same deployment
 */

// Deployment timestamp of the portfolio metrics fix
export const FIX_DEPLOYMENT_TIMESTAMP = new Date('2025-07-10T23:42:09.537Z').getTime() / 1000;

// Contract address where the fix was deployed
export const FIX_CONTRACT_ADDRESS = '0xb1995f8C4Cf5409814d191e444e6433f5B6c712b';

// Enhanced contract address with improved liquidity addition (deployed 2025-07-12T19:09:58.081Z)
export const ENHANCED_CONTRACT_ADDRESS = '0xB0E52DBE2a980815d5622624130199BF511C34B6';
export const ENHANCED_DEPLOYMENT_TIMESTAMP = new Date('2025-07-12T19:09:58.081Z').getTime() / 1000;

/**
 * Correction factor for purchases made before the fix
 * This removes the 5% treasury allocation that was incorrectly included in user stats
 */
export const CORRECTION_FACTOR = 0.95; // Remove 5% treasury allocation

/**
 * Interface for corrected user statistics
 */
export interface CorrectedUserStats extends UserStats {
  correctionApplied: boolean;
  correctedPurchases: number;
  totalPurchases: number;
  correctionDetails: {
    originalTotalTokens: bigint;
    correctedTotalTokens: bigint;
    correctionAmount: bigint;
  };
}

/**
 * Determines if a purchase was made before the portfolio metrics fix
 */
export function isPurchaseBeforeFix(timestamp: number): boolean {
  return timestamp < FIX_DEPLOYMENT_TIMESTAMP;
}

/**
 * Determines if a purchase needs correction due to exchange rate issues
 * NOTE: Exchange rate fix has been deployed, so this should no longer be needed for new purchases
 */
export function isPurchaseWithExchangeRateIssue(timestamp: number, totalTokens: bigint): boolean {
  // Exchange rate fix deployed at 2025-07-10T23:42:09.537Z
  // Only apply exchange rate correction to purchases made before the fix
  const isBeforeExchangeRateFix = timestamp < FIX_DEPLOYMENT_TIMESTAMP;
  const hasInflatedValues = totalTokens > 1000n * 10n ** 18n; // More than 1000 BLOCKS is likely inflated

  return isBeforeExchangeRateFix && hasInflatedValues;
}

/**
 * Applies correction factor to a token amount
 */
export function applyCorrectionFactor(amount: bigint, needsCorrection: boolean): bigint {
  if (!needsCorrection) return amount;
  
  // Apply correction factor: remove 5% treasury allocation
  return (amount * BigInt(Math.floor(CORRECTION_FACTOR * 10000))) / BigInt(10000);
}

/**
 * Corrects user statistics by removing treasury allocation from pre-fix purchases
 */
export function correctUserStats(
  rawStats: UserStats,
  userPurchases: UserPurchase[]
): CorrectedUserStats {
  if (!rawStats || !userPurchases || userPurchases.length === 0) {
    return {
      ...rawStats,
      correctionApplied: false,
      correctedPurchases: 0,
      totalPurchases: 0,
      correctionDetails: {
        originalTotalTokens: rawStats?.totalTokensReceived || 0n,
        correctedTotalTokens: rawStats?.totalTokensReceived || 0n,
        correctionAmount: 0n,
      },
    };
  }

  let correctedTotalTokens = 0n;
  let correctedVestTokens = 0n;
  let correctedPoolTokens = 0n;
  let correctedLPTokens = 0n;
  let correctedPurchases = 0;

  // Process each purchase and apply correction if needed
  for (const purchase of userPurchases) {
    const needsHistoricalCorrection = isPurchaseBeforeFix(purchase.timestamp);
    const needsExchangeRateCorrection = isPurchaseWithExchangeRateIssue(purchase.timestamp, purchase.totalTokens);
    const needsCorrection = needsHistoricalCorrection || needsExchangeRateCorrection;

    if (needsCorrection) {
      correctedPurchases++;
    }

    // Apply correction to token amounts
    // For exchange rate issues, apply a much stronger correction (divide by ~1 million)
    if (needsExchangeRateCorrection) {
      // Exchange rate issue: divide by approximately 1,000,000 to get reasonable values
      const exchangeRateCorrection = 0.000001;
      correctedTotalTokens += BigInt(Math.floor(Number(purchase.totalTokens) * exchangeRateCorrection));
      correctedVestTokens += BigInt(Math.floor(Number(purchase.vestTokens) * exchangeRateCorrection));
      correctedPoolTokens += BigInt(Math.floor(Number(purchase.poolTokens) * exchangeRateCorrection));
      correctedLPTokens += BigInt(Math.floor(Number(purchase.lpTokens) * exchangeRateCorrection));
    } else {
      // Historical correction: apply 0.95 factor
      correctedTotalTokens += applyCorrectionFactor(purchase.totalTokens, needsHistoricalCorrection);
      correctedVestTokens += applyCorrectionFactor(purchase.vestTokens, needsHistoricalCorrection);
      correctedPoolTokens += applyCorrectionFactor(purchase.poolTokens, needsHistoricalCorrection);
      correctedLPTokens += applyCorrectionFactor(purchase.lpTokens, needsHistoricalCorrection);
    }
  }

  const correctionAmount = rawStats.totalTokensReceived - correctedTotalTokens;

  return {
    ...rawStats,
    totalTokensReceived: correctedTotalTokens,
    totalVestTokens: correctedVestTokens,
    totalPoolTokens: correctedPoolTokens,
    totalLPTokens: correctedLPTokens,
    correctionApplied: correctedPurchases > 0,
    correctedPurchases,
    totalPurchases: userPurchases.length,
    correctionDetails: {
      originalTotalTokens: rawStats.totalTokensReceived,
      correctedTotalTokens,
      correctionAmount,
    },
  };
}

/**
 * Calculates the corrected ROI based on corrected portfolio metrics
 */
export function calculateCorrectedROI(correctedStats: CorrectedUserStats): number {
  if (!correctedStats.totalInvested || correctedStats.totalInvested === 0n) {
    return 0;
  }

  // For ROI calculation, we need to estimate the current value of tokens
  // This is a simplified calculation - in practice, you'd use current market prices
  const totalInvestedUSDT = Number(correctedStats.totalInvested) / 1e6; // USDT has 6 decimals
  const totalTokens = Number(correctedStats.totalTokensReceived) / 1e18; // BLOCKS has 18 decimals
  
  // Assuming a base value calculation (this should be replaced with actual market data)
  const estimatedTokenValue = totalTokens * 2.0; // Assuming 2 USDT per BLOCKS as base
  
  return ((estimatedTokenValue - totalInvestedUSDT) / totalInvestedUSDT) * 100;
}

/**
 * Formats correction details for display
 */
export function formatCorrectionDetails(correctedStats: CorrectedUserStats): {
  correctionSummary: string;
  correctionPercentage: string;
  affectedPurchases: string;
} {
  const { correctionDetails, correctedPurchases, totalPurchases } = correctedStats;
  
  const correctionPercentage = correctionDetails.originalTotalTokens > 0n
    ? ((Number(correctionDetails.correctionAmount) / Number(correctionDetails.originalTotalTokens)) * 100).toFixed(2)
    : '0';

  return {
    correctionSummary: `Removed ${(Number(correctionDetails.correctionAmount) / 1e18).toFixed(2)} BLOCKS from inflated historical data`,
    correctionPercentage: `${correctionPercentage}% reduction applied`,
    affectedPurchases: `${correctedPurchases} of ${totalPurchases} purchases corrected`,
  };
}

/**
 * Determines if user stats need correction
 */
export function needsCorrection(userPurchases: UserPurchase[]): boolean {
  if (!userPurchases || userPurchases.length === 0) return false;
  
  return userPurchases.some(purchase => isPurchaseBeforeFix(purchase.timestamp));
}

/**
 * Creates a correction notice for the UI
 */
export function createCorrectionNotice(correctedStats: CorrectedUserStats): {
  show: boolean;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success';
} {
  if (!correctedStats.correctionApplied) {
    return {
      show: false,
      title: '',
      message: '',
      type: 'info',
    };
  }

  const { correctionSummary, affectedPurchases } = formatCorrectionDetails(correctedStats);

  // Check if this is an exchange rate correction (very large correction amount)
  const correctionAmount = Number(correctedStats.correctionDetails.correctionAmount) / 1e18;
  const isExchangeRateCorrection = correctionAmount > 1000000; // More than 1M BLOCKS corrected

  return {
    show: true,
    title: isExchangeRateCorrection ? 'Portfolio Values Corrected (Exchange Rate Issue)' : 'Portfolio Metrics Corrected',
    message: isExchangeRateCorrection
      ? `Your portfolio values have been corrected due to an exchange rate configuration issue. The displayed values now reflect realistic token amounts. ${affectedPurchases}.`
      : `Your portfolio values have been corrected to remove inflated historical data. ${correctionSummary}. ${affectedPurchases}.`,
    type: isExchangeRateCorrection ? 'warning' : 'info',
  };
}
