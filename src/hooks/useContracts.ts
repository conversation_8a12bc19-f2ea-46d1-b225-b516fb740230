import { useCallback, useEffect, useState, useMemo } from 'react';
import { Signer, ContractTransactionResponse } from 'ethers';
import { useWeb3 } from '../providers/Web3Provider';
import { useRefreshListener } from '../contexts/RefreshContext';
import {
  Package,
  PurchaseRecord,
  EnhancedVestingInfo,
  UserStats,
  getAllPackages,
  getPackageById,
  getUSDTBalance,
  getShareTokenBalance,
  getLPTokenBalance,
  getVestingInfo,
  getUserPurchaseHistory,
  getUserRedemptionHistory,
  getUserPortfolioStats,
  formatTokenAmount,
} from '../lib/contracts';
import {
  correctUserStats,
  CorrectedUserStats,
  createCorrectionNotice,
  needsCorrection,
  isPurchaseBeforeFix,
  isPurchaseWithExchangeRateIssue,
  CORRECTION_FACTOR,
} from '../lib/portfolioCorrection';

// Helper function to apply corrections to individual purchase records
function applyCorrectionToPurchase(purchase: PurchaseRecord): PurchaseRecord {
  const needsHistoricalCorrection = isPurchaseBeforeFix(purchase.timestamp);
  const needsExchangeRateCorrection = isPurchaseWithExchangeRateIssue(purchase.timestamp, purchase.totalTokens);

  if (!needsHistoricalCorrection && !needsExchangeRateCorrection) {
    return purchase; // No correction needed
  }

  if (needsExchangeRateCorrection) {
    // Exchange rate issue: divide by approximately 1,000,000 (only for pre-fix purchases)
    const exchangeRateCorrection = 0.000001;
    return {
      ...purchase,
      totalTokens: BigInt(Math.floor(Number(purchase.totalTokens) * exchangeRateCorrection)),
      vestTokens: BigInt(Math.floor(Number(purchase.vestTokens) * exchangeRateCorrection)),
      poolTokens: BigInt(Math.floor(Number(purchase.poolTokens) * exchangeRateCorrection)),
      lpTokens: BigInt(Math.floor(Number(purchase.lpTokens) * exchangeRateCorrection)),
    };
  } else {
    // Historical correction: apply 0.95 factor
    return {
      ...purchase,
      totalTokens: BigInt(Math.floor(Number(purchase.totalTokens) * CORRECTION_FACTOR)),
      vestTokens: BigInt(Math.floor(Number(purchase.vestTokens) * CORRECTION_FACTOR)),
      poolTokens: BigInt(Math.floor(Number(purchase.poolTokens) * CORRECTION_FACTOR)),
      lpTokens: BigInt(Math.floor(Number(purchase.lpTokens) * CORRECTION_FACTOR)),
    };
  }
}

// Hook for package operations
export function usePackages() {
  const { contracts, isConnected } = useWeb3();
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPackages = useCallback(async () => {
    if (!contracts.packageManager) return [];

    setLoading(true);
    setError(null);

    try {
      const packageList = await getAllPackages();
      setPackages(packageList);
      return packageList;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch packages';
      setError(errorMessage);
      console.error('Error fetching packages:', err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [contracts.packageManager]);

  const fetchPackageById = useCallback(async (id: number): Promise<Package | null> => {
    if (!contracts.packageManager) return null;

    try {
      return await getPackageById(id);
    } catch (err) {
      console.error('Error fetching package by ID:', err);
      return null;
    }
  }, [contracts.packageManager]);

  // Auto-refresh on package data events
  useRefreshListener('refreshPackageData', fetchPackages);

  // Initial fetch
  useEffect(() => {
    if (contracts.packageManager) {
      fetchPackages();
    }
  }, [contracts.packageManager, fetchPackages]);

  return {
    packages,
    loading,
    error,
    fetchPackages,
    fetchPackageById,
    refetch: fetchPackages,
  };
}

// Hook for balance operations (Ethers v6 - using bigint)
export function useBalances() {
  const { account, contracts, isConnected } = useWeb3();
  const [balances, setBalances] = useState({
    usdt: 0n,
    share: 0n,
    lp: 0n,
    native: 0n,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchBalances = useCallback(async () => {
    if (!account || !isConnected) {
      setBalances({
        usdt: 0n,
        share: 0n,
        lp: 0n,
        native: 0n,
      });
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const [usdtBalance, shareBalance, lpBalance] = await Promise.all([
        getUSDTBalance(account),
        getShareTokenBalance(account),
        getLPTokenBalance(account),
      ]);

      // Get native balance if provider is available
      let nativeBalance = 0n;
      if (contracts.packageManager?.provider) {
        try {
          nativeBalance = await contracts.packageManager.provider.getBalance(account);
        } catch (err) {
          console.warn('Could not fetch native balance:', err);
        }
      }

      const newBalances = {
        usdt: usdtBalance,
        share: shareBalance,
        lp: lpBalance,
        native: nativeBalance,
      };

      setBalances(newBalances);
      return newBalances;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch balances';
      setError(errorMessage);
      console.error('Error fetching balances:', err);
    } finally {
      setLoading(false);
    }
  }, [account, isConnected, contracts.packageManager]);

  // Auto-refresh on balance events
  useRefreshListener('refreshBalances', fetchBalances);

  // Auto-refresh when account changes
  useEffect(() => {
    fetchBalances();
  }, [account, isConnected, fetchBalances]);

  // Formatted balance getters with correct decimals
  const formattedBalances = {
    usdt: formatTokenAmount(balances.usdt, 6, 2), // USDT uses 6 decimals
    share: formatTokenAmount(balances.share, 18, 4),
    lp: formatTokenAmount(balances.lp, 18, 4),
    native: formatTokenAmount(balances.native, 18, 4),
  };

  return {
    balances,
    formattedBalances,
    loading,
    error,
    fetchBalances,
    refetch: fetchBalances,
  };
}

// Hook for enhanced balances that includes total ShareToken entitlement
export function useEnhancedBalances() {
  const { formattedBalances, loading: balancesLoading, error: balancesError, refetch: refetchBalances } = useBalances();
  const { stats, loading: statsLoading, error: statsError } = useUserPortfolioStats();

  // Use the total tokens received from portfolio stats as the ShareToken entitlement
  // This represents the total ShareTokens the user is entitled to (vesting + pool tokens)
  // and should match the LP token balance since LP tokens are minted 1:1 with total ShareTokens
  const totalShareTokenEntitlement = stats?.totalTokensReceived || 0n;

  const enhancedFormattedBalances = {
    ...formattedBalances,
    // Override share balance to show total entitlement instead of just wallet balance
    shareTotal: formatTokenAmount(totalShareTokenEntitlement, 18, 4),
    shareWallet: formattedBalances.share, // Keep original wallet balance for reference
  };

  return {
    formattedBalances: enhancedFormattedBalances,
    loading: balancesLoading || statsLoading,
    error: balancesError || statsError,
    refetch: refetchBalances,
  };
}

// Hook for vesting operations
export function useVesting() {
  const { account, contracts, signer, isConnected } = useWeb3();
  const [vestingInfo, setVestingInfo] = useState<EnhancedVestingInfo>({
    totalVested: 0n,
    claimable: 0n,
    claimed: 0n,
    remaining: 0n,
    schedule: {
      cliff: 0n,
      duration: 0n,
      start: 0n
    },
    cliffEndTime: 0n,
    vestingEndTime: 0n,
    isCliffPassed: false,
    isFullyVested: false,
    vestingProgress: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchVestingInfo = useCallback(async () => {
    if (!account || !contracts.vestingVault) {
      setVestingInfo({
        totalVested: 0n,
        claimable: 0n,
        claimed: 0n,
        remaining: 0n,
        schedule: {
          cliff: 0n,
          duration: 0n,
          start: 0n
        },
        cliffEndTime: 0n,
        vestingEndTime: 0n,
        isCliffPassed: false,
        isFullyVested: false,
        vestingProgress: 0
      });
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const info = await getVestingInfo(account);
      setVestingInfo(info);
      return info;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch vesting info';
      setError(errorMessage);
      console.error('Error fetching vesting info:', err);
    } finally {
      setLoading(false);
    }
  }, [account, contracts.vestingVault]);

  const claimVested = useCallback(async (): Promise<ContractTransactionResponse | null> => {
    if (!signer || !contracts.vestingVault) {
      throw new Error('Wallet not connected or contract not available');
    }

    try {
      const contractWithSigner = contracts.vestingVault.connect(signer);
      const tx = await contractWithSigner.claim();
      return tx;
    } catch (err) {
      console.error('Error claiming vested tokens:', err);
      throw err;
    }
  }, [signer, contracts.vestingVault]);

  // Auto-refresh on vesting events
  useRefreshListener('refreshVestingData', fetchVestingInfo);

  // Auto-refresh when account changes
  useEffect(() => {
    fetchVestingInfo();
  }, [account, isConnected, fetchVestingInfo]);

  // Formatted vesting info
  const formattedVestingInfo = {
    totalVested: formatTokenAmount(vestingInfo.totalVested, 18, 4),
    claimable: formatTokenAmount(vestingInfo.claimable, 18, 4),
    claimed: formatTokenAmount(vestingInfo.claimed, 18, 4),
    remaining: formatTokenAmount(vestingInfo.remaining, 18, 4),
    cliffEndDate: vestingInfo.cliffEndTime > 0n ? new Date(Number(vestingInfo.cliffEndTime) * 1000) : null,
    vestingEndDate: vestingInfo.vestingEndTime > 0n ? new Date(Number(vestingInfo.vestingEndTime) * 1000) : null,
    startDate: vestingInfo.schedule.start > 0n ? new Date(Number(vestingInfo.schedule.start) * 1000) : null,
    cliffDuration: vestingInfo.schedule.cliff > 0n ? Number(vestingInfo.schedule.cliff) / (24 * 60 * 60) : 0, // days
    totalDuration: vestingInfo.schedule.duration > 0n ? Number(vestingInfo.schedule.duration) / (24 * 60 * 60) : 0, // days
    vestingProgress: vestingInfo.vestingProgress,
    isCliffPassed: vestingInfo.isCliffPassed,
    isFullyVested: vestingInfo.isFullyVested,
  };

  return {
    vestingInfo,
    formattedVestingInfo,
    loading,
    error,
    fetchVestingInfo,
    claimVested,
    refetch: fetchVestingInfo,
  };
}

// Hook for transaction operations
export function useTransactions() {
  const { signer, contracts, account, isCorrectNetwork } = useWeb3();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeTransaction = useCallback(async (
    transactionFn: () => Promise<any>,
    options?: {
      onSuccess?: (tx: any) => void;
      onError?: (error: Error) => void;
      waitForConfirmation?: boolean;
    }
  ): Promise<any | null> => {
    if (!signer || !account) {
      const error = new Error('Wallet not connected');
      options?.onError?.(error);
      throw error;
    }

    if (!isCorrectNetwork) {
      const error = new Error('Wrong network. Please switch to BSC Testnet');
      options?.onError?.(error);
      throw error;
    }

    setLoading(true);
    setError(null);

    try {
      const tx = await transactionFn();

      options?.onSuccess?.(tx);

      if (options?.waitForConfirmation) {
        await tx.wait();
      }

      return tx;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Transaction failed');
      setError(error.message);
      options?.onError?.(error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [signer, account, isCorrectNetwork]);

  return {
    executeTransaction,
    loading,
    error,
  };
}

// Hook for network validation
export function useNetworkValidation() {
  const { chainId, isConnected, switchToCorrectNetwork } = useWeb3();
  const [isValidating, setIsValidating] = useState(false);

  const isCorrectNetwork = chainId === 97; // BSC Testnet

  const validateAndSwitchNetwork = useCallback(async (): Promise<boolean> => {
    if (isCorrectNetwork) return true;

    setIsValidating(true);
    try {
      const success = await switchToCorrectNetwork();
      return success;
    } catch (error) {
      console.error('Failed to switch network:', error);
      return false;
    } finally {
      setIsValidating(false);
    }
  }, [isCorrectNetwork, switchToCorrectNetwork]);

  return {
    isCorrectNetwork,
    isConnected,
    chainId,
    isValidating,
    validateAndSwitchNetwork,
  };
}

// Hook for user purchase history
export function usePurchaseHistory() {
  const { account, contracts, isConnected } = useWeb3();
  const [purchases, setPurchases] = useState<PurchaseRecord[]>([]);
  const [redemptions, setRedemptions] = useState<Array<{
    lpAmount: bigint;
    timestamp: number;
    blockNumber: number;
    transactionHash: string;
  }>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPurchaseHistory = useCallback(async () => {
    if (!account || !isConnected) {
      setPurchases([]);
      setRedemptions([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching purchase and redemption history...');

      // Fetch purchase and redemption history with individual error handling
      const [purchaseResult, redemptionResult] = await Promise.allSettled([
        getUserPurchaseHistory(account),
        getUserRedemptionHistory(account),
      ]);

      // Handle purchase history result
      let purchaseHistory: PurchaseRecord[] = [];
      if (purchaseResult.status === 'fulfilled') {
        purchaseHistory = purchaseResult.value;
        console.log(`Successfully fetched ${purchaseHistory.length} purchase records`);
      } else {
        console.error('Failed to fetch purchase history:', purchaseResult.reason);
      }

      // Handle redemption history result
      let redemptionHistory: Array<{
        lpAmount: bigint;
        timestamp: number;
        blockNumber: number;
        transactionHash: string;
      }> = [];
      if (redemptionResult.status === 'fulfilled') {
        redemptionHistory = redemptionResult.value;
        console.log(`Successfully fetched ${redemptionHistory.length} redemption records`);
      } else {
        console.error('Failed to fetch redemption history:', redemptionResult.reason);
      }

      // Apply corrections to purchase history
      const correctedPurchases = purchaseHistory.map(applyCorrectionToPurchase);

      setPurchases(correctedPurchases);
      setRedemptions(redemptionHistory);

      // Set error only if both failed
      if (purchaseResult.status === 'rejected' && redemptionResult.status === 'rejected') {
        setError('Failed to fetch transaction history. Please try again later.');
      } else if (purchaseResult.status === 'rejected') {
        setError('Failed to fetch purchase history. Redemption history loaded successfully.');
      } else if (redemptionResult.status === 'rejected') {
        setError('Failed to fetch redemption history. Purchase history loaded successfully.');
      }

      return { purchases: correctedPurchases, redemptions: redemptionHistory };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch transaction history';
      setError(errorMessage);
      console.error('Error fetching purchase history:', err);

      // Still return empty arrays so the UI can render
      setPurchases([]);
      setRedemptions([]);
    } finally {
      setLoading(false);
    }
  }, [account, isConnected]);

  // Auto-refresh on purchase/redemption events
  useRefreshListener('refreshPurchaseHistory', fetchPurchaseHistory);

  // Auto-refresh when account changes
  useEffect(() => {
    fetchPurchaseHistory();
  }, [account, isConnected, fetchPurchaseHistory]);

  // Calculate summary metrics
  const summary = useMemo(() => {
    const totalInvested = purchases.reduce((sum, purchase) => sum + purchase.usdtAmount, 0n);
    const totalTokensReceived = purchases.reduce((sum, purchase) => sum + purchase.totalTokens, 0n);
    const totalVestTokens = purchases.reduce((sum, purchase) => sum + purchase.vestTokens, 0n);
    const totalPoolTokens = purchases.reduce((sum, purchase) => sum + purchase.poolTokens, 0n);
    const totalLPTokens = purchases.reduce((sum, purchase) => sum + purchase.lpTokens, 0n);
    const totalRedemptions = redemptions.reduce((sum, redemption) => sum + redemption.lpAmount, 0n);

    // Calculate performance metrics
    const currentLPTokens = totalLPTokens - totalRedemptions;

    // Simple ROI calculation based on tokens received vs USDT invested
    // Note: This is a basic calculation and doesn't account for current market prices
    let roi = 0;
    if (totalInvested > 0n) {
      // Assuming 1:1 token to USDT ratio for basic ROI calculation
      // In a real scenario, you'd want to fetch current token prices

      // Convert to numbers safely to avoid BigInt overflow
      const investedNumber = Number(totalInvested) / 1e6; // USDT has 6 decimals
      const tokensNumber = Number(totalTokensReceived) / 1e18; // ShareTokens have 18 decimals

      // Calculate ROI as percentage
      if (investedNumber > 0) {
        roi = ((tokensNumber - investedNumber) / investedNumber) * 100;
      }
    }

    return {
      totalInvested,
      totalTokensReceived,
      totalVestTokens,
      totalPoolTokens,
      totalLPTokens,
      totalRedemptions,
      currentLPTokens,
      purchaseCount: purchases.length,
      redemptionCount: redemptions.length,
      roi,
    };
  }, [purchases, redemptions]);

  // Formatted summary for display
  const formattedSummary = {
    totalInvested: formatTokenAmount(summary.totalInvested, 6, 2), // USDT uses 6 decimals
    totalTokensReceived: formatTokenAmount(summary.totalTokensReceived, 18, 4),
    totalVestTokens: formatTokenAmount(summary.totalVestTokens, 18, 4),
    totalPoolTokens: formatTokenAmount(summary.totalPoolTokens, 18, 4),
    totalLPTokens: formatTokenAmount(summary.totalLPTokens, 18, 4),
    totalRedemptions: formatTokenAmount(summary.totalRedemptions, 18, 4),
    currentLPTokens: formatTokenAmount(summary.currentLPTokens, 18, 4),
    purchaseCount: summary.purchaseCount,
    redemptionCount: summary.redemptionCount,
    roi: summary.roi,
  };

  // Formatted redemptions for display
  const formattedRedemptions = redemptions.map(redemption => ({
    ...redemption,
    formattedAmount: formatTokenAmount(redemption.lpAmount, 18, 4),
    date: new Date(redemption.timestamp * 1000),
  }));

  return {
    purchases,
    redemptions,
    formattedRedemptions,
    summary,
    formattedSummary,
    loading,
    error,
    fetchPurchaseHistory,
    refetch: fetchPurchaseHistory,
  };
}

// New hook for efficient user portfolio stats using smart contract view functions
export function useUserPortfolioStats() {
  const { account, isConnected } = useWeb3();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    if (!account || !isConnected) {
      setStats(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching user portfolio stats from smart contract...');
      const userStats = await getUserPortfolioStats(account);

      if (userStats) {
        setStats(userStats);
        console.log('Successfully fetched user portfolio stats');
      } else {
        setError('Failed to fetch portfolio stats');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch portfolio stats';
      setError(errorMessage);
      console.error('Error fetching portfolio stats:', err);
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, [account, isConnected]);

  // Auto-refresh on purchase/redemption events
  useRefreshListener('refreshPortfolioStats', fetchStats);

  // Auto-refresh when account changes
  useEffect(() => {
    fetchStats();
  }, [account, isConnected, fetchStats]);

  // Formatted stats for display
  const formattedStats = useMemo(() => {
    if (!stats) return null;

    return {
      totalInvested: formatTokenAmount(stats.totalInvested, 6, 2), // USDT has 6 decimals
      totalTokensReceived: formatTokenAmount(stats.totalTokensReceived, 18, 4),
      totalVestTokens: formatTokenAmount(stats.totalVestTokens, 18, 4),
      totalPoolTokens: formatTokenAmount(stats.totalPoolTokens, 18, 4),
      totalLPTokens: formatTokenAmount(stats.totalLPTokens, 18, 4),
      totalReferralRewards: formatTokenAmount(stats.totalReferralRewards, 18, 4),
      totalRedemptions: formatTokenAmount(stats.totalRedemptions, 18, 4),
      purchaseCount: Number(stats.purchaseCount),
      redemptionCount: Number(stats.redemptionCount),
    };
  }, [stats]);

  return {
    stats,
    formattedStats,
    loading,
    error,
    fetchStats,
    refetch: fetchStats,
  };
}

// Enhanced hook for user portfolio stats with correction for inflated historical data
export function useCorrectedPortfolioStats() {
  const { account, isConnected } = useWeb3();
  const [correctedStats, setCorrectedStats] = useState<CorrectedUserStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCorrectedStats = useCallback(async () => {
    if (!account || !isConnected) {
      setCorrectedStats(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching corrected user portfolio stats...');

      // Fetch both raw stats and purchase history for correction
      const [userStats, purchaseHistory] = await Promise.all([
        getUserPortfolioStats(account),
        getUserPurchaseHistory(account),
      ]);

      if (userStats) {
        // Apply correction logic
        const corrected = correctUserStats(userStats, purchaseHistory);
        setCorrectedStats(corrected);

        if (corrected.correctionApplied) {
          console.log(`Portfolio correction applied: ${corrected.correctedPurchases} purchases corrected`);
        } else {
          console.log('No portfolio correction needed');
        }
      } else {
        setError('Failed to fetch portfolio stats');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch corrected portfolio stats';
      setError(errorMessage);
      console.error('Error fetching corrected portfolio stats:', err);
      setCorrectedStats(null);
    } finally {
      setLoading(false);
    }
  }, [account, isConnected]);

  // Auto-refresh on purchase/redemption events
  useRefreshListener('refreshPortfolioStats', fetchCorrectedStats);
  useRefreshListener('refreshPurchaseHistory', fetchCorrectedStats);

  // Auto-refresh when account changes
  useEffect(() => {
    fetchCorrectedStats();
  }, [account, isConnected, fetchCorrectedStats]);

  // Formatted corrected stats for display
  const formattedCorrectedStats = useMemo(() => {
    if (!correctedStats) return null;

    return {
      totalInvested: formatTokenAmount(correctedStats.totalInvested, 6, 2), // USDT has 6 decimals
      totalTokensReceived: formatTokenAmount(correctedStats.totalTokensReceived, 18, 4),
      totalVestTokens: formatTokenAmount(correctedStats.totalVestTokens, 18, 4),
      totalPoolTokens: formatTokenAmount(correctedStats.totalPoolTokens, 18, 4),
      totalLPTokens: formatTokenAmount(correctedStats.totalLPTokens, 18, 4),
      totalReferralRewards: formatTokenAmount(correctedStats.totalReferralRewards, 18, 4),
      totalRedemptions: formatTokenAmount(correctedStats.totalRedemptions, 18, 4),
      purchaseCount: Number(correctedStats.purchaseCount),
      redemptionCount: Number(correctedStats.redemptionCount),
      // Correction-specific fields
      correctionApplied: correctedStats.correctionApplied,
      correctedPurchases: correctedStats.correctedPurchases,
      totalPurchases: correctedStats.totalPurchases,
      originalTotalTokens: formatTokenAmount(correctedStats.correctionDetails.originalTotalTokens, 18, 4),
      correctionAmount: formatTokenAmount(correctedStats.correctionDetails.correctionAmount, 18, 4),
    };
  }, [correctedStats]);

  // Correction notice for UI
  const correctionNotice = useMemo(() => {
    if (!correctedStats) return null;
    return createCorrectionNotice(correctedStats);
  }, [correctedStats]);

  return {
    correctedStats,
    formattedCorrectedStats,
    correctionNotice,
    loading,
    error,
    fetchCorrectedStats,
    refetch: fetchCorrectedStats,
  };
}
